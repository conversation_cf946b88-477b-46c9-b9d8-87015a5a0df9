{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "biome check src/", "lint:fix": "biome check --write src/", "format": "biome format --write src/", "check-types": "tsc --noEmit", "db:push": "prisma db push --schema ./prisma/schema/schema.prisma", "db:studio": "prisma studio --schema ./prisma/schema/schema.prisma", "db:generate": "prisma generate --schema ./prisma/schema/schema.prisma", "db:migrate": "prisma migrate dev --schema ./prisma/schema/schema.prisma", "db:seed": "tsx ./prisma/seed.ts", "db:seed-models": "tsx src/scripts/seed-ai-models.ts", "db:reset": "prisma migrate reset --schema ./prisma/schema/schema.prisma", "db:sync-users": "tsx src/scripts/sync-clerk-users.ts", "migrate:clerk-billing": "tsx src/scripts/migrate-to-clerk-billing.ts migrate", "migrate:clerk-billing:rollback": "tsx src/scripts/migrate-to-clerk-billing.ts rollback", "migrate:clerk-billing:verify": "tsx src/scripts/migrate-to-clerk-billing.ts verify", "validate-env": "tsx src/scripts/validate-env.ts", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "test:unit": "vitest --config vitest.unit.config.ts", "test:unit:run": "vitest run --config vitest.unit.config.ts", "test:unit:watch": "vitest --watch --config vitest.unit.config.ts", "test:setup": "npm run db:generate && npm run test:db:setup", "test:db:setup": "DATABASE_URL=\"postgresql://user:password@localhost:5432/buddychip_test\" prisma migrate deploy --schema ./prisma/schema/schema.prisma"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@clerk/backend": "^2.1.0", "@clerk/elements": "^0.23.33", "@clerk/nextjs": "^6.22.0", "@clerk/themes": "^2.2.50", "@openrouter/ai-sdk-provider": "^0.7.2", "@prisma/client": "^6.9.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@sentry/nextjs": "^9.30.0", "@sentry/node": "^9.30.0", "@sentry/react": "^9.30.0", "@tanstack/react-form": "^1.3.2", "@tanstack/react-query": "^5.80.5", "@trpc/client": "^11.4.2", "@trpc/react-query": "^11.4.2", "@trpc/server": "^11.4.2", "@trpc/tanstack-react-query": "^11.4.2", "@types/validator": "^13.15.2", "@upstash/ratelimit": "^2.0.5", "@vercel/kv": "^3.0.0", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "framer-motion": "^12.19.1", "lottie-react": "^2.4.1", "lucide-react": "^0.487.0", "mem0ai": "^1.0.5", "next": "15.3.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "sonner": "^2.0.3", "svix": "^1.67.0", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "validator": "^13.15.15", "zod": "^3.25.67"}, "devDependencies": {"@clerk/testing": "^1.8.1", "@tailwindcss/postcss": "^4", "@tanstack/react-query-devtools": "^5.80.5", "@testing-library/jest-dom": "^6.6.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19.1.6", "@types/supertest": "^6.0.3", "@vitest/ui": "^3.2.4", "msw": "^2.10.2", "msw-trpc": "2.0.0-beta.1", "prisma": "^6.9.0", "supertest": "^7.1.1", "tailwindcss": "^4.1.10", "tsx": "^4.20.3", "typescript": "^5", "vitest": "^3.2.4"}, "trustedDependencies": ["supabase"]}