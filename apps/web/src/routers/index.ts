import { createTRPCRouter, publicProcedure } from "../lib/trpc";
import { accountsRouter } from "./accounts";
import { benjiRouter } from "./benji";
import { billingRouter } from "./billing";
import { cryptoRouter } from "./crypto";
import { mentionsRouter } from "./mentions";
import { twitterRouter } from "./twitter";
import { userRouter } from "./user";

export const appRouter = createTRPCRouter({
  healthCheck: publicProcedure.query(() => {
    return "OK";
  }),
  benji: benjiRouter,
  user: userRouter,
  accounts: accountsRouter,
  mentions: mentionsRouter,
  twitter: twitterRouter,
  billing: billingRouter,
  crypto: cryptoRouter,
});

export type AppRouter = typeof appRouter;
