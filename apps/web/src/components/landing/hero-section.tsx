"use client";

import { useUser } from "@clerk/nextjs";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useEffect, useRef } from "react";
import { MdArrowOutward } from "react-icons/md";
import PrimaryButton from "../atoms/button";

export function HeroSection() {
  const videoRef = useRef<HTMLVideoElement>(null);
  const { user, isLoaded } = useUser();
  const router = useRouter();

  const handleStartJourney = () => {
    if (user) {
      router.push("/dashboard");
    } else {
      router.push("/sign-in");
    }
  };

  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      const handleLoadedMetadata = () => {
        const playPromise = video.play();
        if (playPromise !== undefined) {
          playPromise.catch(() => {
            console.log("Video autoplay failed - user interaction required");
          });
        }
      };

      video.addEventListener("loadedmetadata", handleLoadedMetadata);
      return () =>
        video.removeEventListener("loadedmetadata", handleLoadedMetadata);
    }
  }, []);

  return (
    <header className="min-h-[100vh] flex flex-col items-end justify-between relative overflow-hidden bg-app-stroke">
      {/* Desktop Content */}
      <div className="z-10 hidden lg:flex flex-1 flex-col items-start justify-end p-6 xl:p-8">
        <PrimaryButton
          icon={MdArrowOutward}
          onClick={handleStartJourney}
          iconVariant="tertiary"
          className="mb-6"
        >
          Start your AI Journey!
        </PrimaryButton>
        <h1 className="w-[85%] xl:w-[80%] tracking-[1.5px] text-app-secondary text-[clamp(3rem,6vw,4.875rem)] leading-[1.1] font-medium">
          BuddyChip — AI-Powered Twitter Assistant That Never Sleeps
        </h1>
      </div>

      {/* Tablet Content */}
      <div className="z-10 hidden md:flex lg:hidden flex-1 flex-col items-center justify-center p-6 text-center">
        {/* Dark overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/50 via-black/20 to-black/50"></div>

        <div className="relative z-20 flex flex-col items-center gap-8 max-w-2xl">
          <h1 className="w-full text-center tracking-[1px] text-app-secondary text-[clamp(2.5rem,5vw,3.5rem)] leading-tight font-medium">
            BuddyChip — AI-Powered Twitter Assistant
          </h1>
          <div className="max-w-lg rounded-lg border border-app-stroke bg-app-stroke/80 p-4 text-base text-app-secondary/90">
            AI-powered mentions, multi-model responses, and real-time analytics
            — all in one powerful Twitter assistant.
          </div>
          <PrimaryButton
            icon={MdArrowOutward}
            onClick={handleStartJourney}
            iconVariant="tertiary"
          >
            Start your AI Journey!
          </PrimaryButton>
        </div>
      </div>

      {/* Mobile Content */}
      <div className="z-10 md:hidden w-full flex flex-col justify-between flex-1 p-4 pt-6 pb-8">
        {/* Dark overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/30 to-black/60"></div>

        {/* Title at top */}
        <div className="relative z-20 flex flex-col items-center mt-6 sm:mt-8">
          <h1 className="w-full text-center tracking-[0.5px] text-app-secondary text-[clamp(1.75rem,7vw,2.25rem)] leading-tight font-medium px-2">
            BuddyChip — AI-Powered Twitter Assistant
          </h1>
        </div>

        {/* Bottom content */}
        <div className="relative z-20 flex flex-col items-center gap-6 sm:gap-8">
          <div className="max-w-sm rounded-lg border border-app-stroke bg-app-stroke/90 p-4 text-sm sm:text-base text-app-secondary/90 backdrop-blur-sm">
            AI-powered mentions, multi-model responses, and real-time analytics
            — all in one powerful Twitter assistant.
          </div>
          <PrimaryButton
            icon={MdArrowOutward}
            onClick={handleStartJourney}
            iconVariant="tertiary"
          >
            Start your AI Journey!
          </PrimaryButton>
        </div>
      </div>

      <div className="z-10 hidden lg:block absolute top-[110px] xl:top-[120px] right-[24px] xl:right-[32px] max-w-[400px] xl:max-w-[440px] rounded-lg border border-app-stroke bg-app-stroke/90 p-4 xl:p-5 text-app-secondary/90 backdrop-blur-sm">
        AI-powered mentions, multi-model responses, and real-time analytics —
        all in one powerful Twitter assistant.
      </div>

      <video
        ref={videoRef}
        className="w-full h-full absolute top-0 left-0 object-cover"
        autoPlay
        muted
        loop
        playsInline
        controls={false}
        preload="metadata"
        onError={() => console.log("Video failed to load")}
        onLoadedMetadata={() => console.log("Video metadata loaded")}
      >
        <source
          src="https://yo2pcnft8a.ufs.sh/f/nskFA2JaD20hOZDzeVHTrvafW1kp0QAyPq7j4wnhxbNoV63e"
          type="video/mp4"
        />
        Your browser does not support the video tag.
      </video>
    </header>
  );
}
